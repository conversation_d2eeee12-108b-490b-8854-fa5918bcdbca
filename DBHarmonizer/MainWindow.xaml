<Window x:Class="DBHarmonizer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DBHarmonizer"
        xmlns:converters="clr-namespace:DBHarmonizer.Converters"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        mc:Ignorable="d"
        Title="DBHarmonizer - Oracle Database Schema Structure Compare" 
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1200"
        Background="{StaticResource MainBackgroundBrush}"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Window.Resources>
        <!-- Converters -->
        <converters:ComparisonStatusToColorConverter x:Key="StatusToColorConverter"/>
        <converters:ComparisonStatusToIconConverter x:Key="StatusToIconConverter"/>
        
        <!-- Fluent Design Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Primary Button Style -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{StaticResource SecondaryNavyBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{StaticResource DarkNavyBrush}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="#E5E7EB"/>
                                <Setter Property="Foreground" Value="#9CA3AF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Secondary Button Style -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{StaticResource BorderBrush}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="BorderBrush" Value="#D1D5DB"/>
                                <Setter Property="Foreground" Value="#9CA3AF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Step Indicator Styles -->
        <Style x:Key="StepIndicatorStyle" TargetType="Border">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Background" Value="{StaticResource ProgressBackgroundBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>
        
        <Style x:Key="ActiveStepIndicatorStyle" TargetType="Border" BasedOn="{StaticResource StepIndicatorStyle}">
            <Setter Property="Background" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryNavyBrush}"/>
        </Style>
        
        <Style x:Key="CompletedStepIndicatorStyle" TargetType="Border" BasedOn="{StaticResource StepIndicatorStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource SuccessBrush}"/>
        </Style>
        
        <!-- Header Text Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryNavyBrush}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>
        
        <!-- Instruction Text Style -->
        <Style x:Key="InstructionTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryNavyBrush}" Padding="24,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🗄️" FontSize="28" Foreground="White" Margin="0,0,16,0" VerticalAlignment="Center"/>
                    <TextBlock Text="Oracle Database Schema Structure Compare" 
                               FontSize="24" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SettingsButton" Content="⚙️ Settings" Style="{StaticResource SecondaryButtonStyle}" 
                            Background="{StaticResource SecondaryNavyBrush}" BorderBrush="White" Foreground="White"
                            Margin="0,0,12,0" Click="SettingsButton_Click"/>
                    <Button x:Name="AboutButton" Content="ℹ️ About" Style="{StaticResource SecondaryButtonStyle}" 
                            Background="{StaticResource SecondaryNavyBrush}" BorderBrush="White" Foreground="White"
                            Click="AboutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <telerik:RadTabControl Grid.Row="1" x:Name="MainTabControl" 
                               Margin="0" 
                               SelectionChanged="MainTabControl_SelectionChanged"
                               TabStripPlacement="Top">
            
            <!-- Step 1: Database Connections -->
            <telerik:RadTabItem x:Name="ConnectionsTab" Header="1. Database Connections">
                <telerik:RadTabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <Border x:Name="Step1Indicator" Style="{StaticResource ActiveStepIndicatorStyle}" Margin="0,0,12,0">
                                <TextBlock Text="1" FontWeight="Bold" FontSize="14" 
                                           Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="Database Connections" VerticalAlignment="Center" FontSize="14" FontWeight="SemiBold"/>
                        </StackPanel>
                    </DataTemplate>
                </telerik:RadTabItem.HeaderTemplate>
                
                <Border Style="{StaticResource CardStyle}" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Header -->
                        <StackPanel Grid.Row="0">
                            <TextBlock Text="Configure Database Connections" Style="{StaticResource HeaderTextStyle}"/>
                            <TextBlock Text="Enter connection details for both source and target Oracle databases. Test each connection to ensure they're working properly before proceeding." 
                                       Style="{StaticResource InstructionTextStyle}"/>
                        </StackPanel>
                        
                        <!-- Progress Bar -->
                        <ProgressBar Grid.Row="1" x:Name="ConnectionProgressBar" 
                                     Height="4" Margin="0,0,0,24" Visibility="Collapsed"
                                     Background="{StaticResource ProgressBackgroundBrush}"
                                     Foreground="{StaticResource ProgressBrush}"/>
                        
                        <!-- Connection Forms -->
                        <Grid Grid.Row="2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="32"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Source Database -->
                            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,16,0">
                                <StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Source Database" Style="{StaticResource SectionHeaderStyle}"/>
                                        <Border Grid.Column="1" x:Name="SourceStatusIndicator" Width="16" Height="16" 
                                                CornerRadius="8" Background="{StaticResource ProgressBackgroundBrush}"/>
                                    </Grid>
                                    
                                    <TextBlock Text="This is the database you want to compare FROM (the reference database)." 
                                               Style="{StaticResource InstructionTextStyle}" Margin="0,0,0,16"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="SourceServerTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,12"
                                                 Tag="e.g., localhost or *************"
                                                 TextChanged="SourceServerTextBox_TextChanged"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="SourcePortTextBox" Grid.Row="1" Grid.Column="1" Text="1521" Margin="0,0,0,12"
                                                 Tag="Default: 1521"
                                                 TextChanged="SourcePortTextBox_TextChanged"/>

                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Service:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="SourceServiceTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,12"
                                                 Tag="e.g., ORCL, XE, or your service name"
                                                 TextChanged="SourceServiceTextBox_TextChanged"/>

                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="SourceUsernameTextBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,12"
                                                 Tag="Database username"
                                                 TextChanged="SourceUsernameTextBox_TextChanged"/>
                                        
                                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <PasswordBox x:Name="SourcePasswordBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,16"
                                                     PasswordChanged="SourcePasswordBox_PasswordChanged"/>
                                        
                                        <Button x:Name="TestSourceButton" Grid.Row="5" Grid.Column="1" Content="🔍 Test Connection" 
                                                Style="{StaticResource PrimaryButtonStyle}" Click="TestSourceButton_Click"
                                                HorizontalAlignment="Left" IsEnabled="False"/>
                                    </Grid>
                                    
                                    <!-- Connection Status -->
                                    <Border x:Name="SourceConnectionStatus" Visibility="Collapsed" 
                                            Background="{StaticResource SecondaryBackgroundBrush}" 
                                            CornerRadius="6" Padding="12" Margin="0,16,0,0">
                                        <TextBlock x:Name="SourceStatusText" FontSize="13"/>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- Connection Arrow -->
                            <TextBlock Grid.Column="1" Text="⇄" FontSize="24" 
                                       Foreground="{StaticResource SecondaryNavyBrush}"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>

                            <!-- Target Database -->
                            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Margin="16,0,0,0">
                                <StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Target Database" Style="{StaticResource SectionHeaderStyle}"/>
                                        <Border Grid.Column="1" x:Name="TargetStatusIndicator" Width="16" Height="16" 
                                                CornerRadius="8" Background="{StaticResource ProgressBackgroundBrush}"/>
                                    </Grid>
                                    
                                    <TextBlock Text="This is the database you want to compare TO (the comparison target)." 
                                               Style="{StaticResource InstructionTextStyle}" Margin="0,0,0,16"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="TargetServerTextBox" Grid.Row="0" Grid.Column="1" Margin="0,0,0,12"
                                                 Tag="e.g., localhost or *************"
                                                 TextChanged="TargetServerTextBox_TextChanged"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="TargetPortTextBox" Grid.Row="1" Grid.Column="1" Text="1521" Margin="0,0,0,12"
                                                 Tag="Default: 1521"
                                                 TextChanged="TargetPortTextBox_TextChanged"/>

                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Service:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="TargetServiceTextBox" Grid.Row="2" Grid.Column="1" Margin="0,0,0,12"
                                                 Tag="e.g., ORCL, XE, or your service name"
                                                 TextChanged="TargetServiceTextBox_TextChanged"/>

                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Username:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <TextBox x:Name="TargetUsernameTextBox" Grid.Row="3" Grid.Column="1" Margin="0,0,0,12"
                                                 Tag="Database username"
                                                 TextChanged="TargetUsernameTextBox_TextChanged"/>
                                        
                                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Password:" VerticalAlignment="Center" Margin="0,0,12,12" FontWeight="SemiBold"/>
                                        <PasswordBox x:Name="TargetPasswordBox" Grid.Row="4" Grid.Column="1" Margin="0,0,0,16"
                                                     PasswordChanged="TargetPasswordBox_PasswordChanged"/>
                                        
                                        <Button x:Name="TestTargetButton" Grid.Row="5" Grid.Column="1" Content="🔍 Test Connection" 
                                                Style="{StaticResource PrimaryButtonStyle}" Click="TestTargetButton_Click"
                                                HorizontalAlignment="Left" IsEnabled="False"/>
                                    </Grid>
                                    
                                    <!-- Connection Status -->
                                    <Border x:Name="TargetConnectionStatus" Visibility="Collapsed" 
                                            Background="{StaticResource SecondaryBackgroundBrush}" 
                                            CornerRadius="6" Padding="12" Margin="0,16,0,0">
                                        <TextBlock x:Name="TargetStatusText" FontSize="13"/>
                                    </Border>
                                </StackPanel>
                            </Border>
                        </Grid>
                        
                        <!-- Navigation -->
                        <Grid Grid.Row="3" Margin="0,24,0,0">
                            <Button x:Name="NextToComparisonButton" Content="Next: Configure Comparison →" 
                                    Style="{StaticResource PrimaryButtonStyle}" 
                                    HorizontalAlignment="Right" IsEnabled="False"
                                    Click="NextToComparisonButton_Click"/>
                        </Grid>
                    </Grid>
                </Border>
            </telerik:RadTabItem>
            
            <!-- Step 2: Schema Comparison Configuration -->
            <telerik:RadTabItem x:Name="ComparisonTab" Header="2. Schema Comparison" IsEnabled="False">
                <telerik:RadTabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <Border x:Name="Step2Indicator" Style="{StaticResource StepIndicatorStyle}" Margin="0,0,12,0">
                                <TextBlock Text="2" FontWeight="Bold" FontSize="14" 
                                           Foreground="{StaticResource SecondaryTextBrush}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="Schema Comparison" VerticalAlignment="Center" FontSize="14" FontWeight="SemiBold"/>
                        </StackPanel>
                    </DataTemplate>
                </telerik:RadTabItem.HeaderTemplate>
                
                <Border Style="{StaticResource CardStyle}" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- Header -->
                        <StackPanel Grid.Row="0">
                            <TextBlock Text="Configure Schema Comparison" Style="{StaticResource HeaderTextStyle}"/>
                            <TextBlock Text="Select the types of database objects you want to compare and configure comparison options." 
                                       Style="{StaticResource InstructionTextStyle}"/>
                        </StackPanel>
                        
                        <!-- Progress Bar -->
                        <ProgressBar Grid.Row="1" x:Name="ComparisonProgressBar" 
                                     Height="4" Margin="0,0,0,24" Visibility="Collapsed"
                                     Background="{StaticResource ProgressBackgroundBrush}"
                                     Foreground="{StaticResource ProgressBrush}"/>
                        
                        <!-- Configuration Options -->
                        <Grid Grid.Row="2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Object Types Selection -->
                            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,16,0">
                                <StackPanel>
                                    <TextBlock Text="Object Types to Compare" Style="{StaticResource SectionHeaderStyle}"/>
                                    <TextBlock Text="Select which types of database objects you want to include in the comparison." 
                                               Style="{StaticResource InstructionTextStyle}"/>
                                    
                                    <StackPanel x:Name="ObjectTypesPanel">
                                        <CheckBox x:Name="TablesCheckBox" Content="Tables" IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="ViewsCheckBox" Content="Views" IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="IndexesCheckBox" Content="Indexes" IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="SequencesCheckBox" Content="Sequences" IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="TriggersCheckBox" Content="Triggers" IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="ProceduresCheckBox" Content="Procedures" IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="FunctionsCheckBox" Content="Functions" IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="PackagesCheckBox" Content="Packages" IsChecked="True" Margin="0,0,0,8"/>
                                    </StackPanel>
                                    
                                    <Grid Margin="0,16,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Button Grid.Column="0" Content="Select All" Style="{StaticResource SecondaryButtonStyle}" 
                                                Margin="0,0,8,0" Click="SelectAllObjectTypes_Click"/>
                                        <Button Grid.Column="1" Content="Clear All" Style="{StaticResource SecondaryButtonStyle}" 
                                                Margin="8,0,0,0" Click="ClearAllObjectTypes_Click"/>
                                    </Grid>
                                </StackPanel>
                            </Border>
                            
                            <!-- Comparison Options -->
                            <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="16,0,0,0">
                                <StackPanel>
                                    <TextBlock Text="Comparison Options" Style="{StaticResource SectionHeaderStyle}"/>
                                    <TextBlock Text="Configure additional options for the schema comparison process." 
                                               Style="{StaticResource InstructionTextStyle}"/>
                                    
                                    <StackPanel>
                                        <TextBlock Text="Owner Filter (Optional):" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                        <TextBox x:Name="OwnerFilterTextBox" 
                                                 Tag="Leave empty to compare all schemas"
                                                 Margin="0,0,0,16"/>
                                        
                                        <CheckBox x:Name="IncludeSystemObjectsCheckBox" Content="Include System Objects" 
                                                  IsChecked="False" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="CompareDefinitionsCheckBox" Content="Compare Object Definitions" 
                                                  IsChecked="True" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="GenerateReportCheckBox" Content="Generate Detailed Report" 
                                                  IsChecked="True" Margin="0,0,0,16"/>
                                    </StackPanel>
                                    
                                    <!-- Connection Summary -->
                                    <Border Background="{StaticResource SecondaryBackgroundBrush}" 
                                            CornerRadius="6" Padding="16" Margin="0,16,0,0">
                                        <StackPanel>
                                            <TextBlock Text="Connection Summary:" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                            <TextBlock x:Name="ConnectionSummaryText" FontSize="13" 
                                                       Foreground="{StaticResource SecondaryTextBrush}"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>
                        </Grid>
                        
                        <!-- Navigation -->
                        <Grid Grid.Row="3" Margin="0,24,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button Grid.Column="0" x:Name="BackToConnectionsButton" Content="← Back to Connections" 
                                    Style="{StaticResource SecondaryButtonStyle}" Click="BackToConnectionsButton_Click"/>
                            
                            <Button Grid.Column="2" x:Name="StartComparisonButton" Content="🚀 Start Comparison" 
                                    Style="{StaticResource PrimaryButtonStyle}" Click="StartComparisonButton_Click"/>
                        </Grid>
                    </Grid>
                </Border>
            </telerik:RadTabItem>
            
            <!-- Step 3: Results -->
            <telerik:RadTabItem x:Name="ResultsTab" Header="3. Results" IsEnabled="False">
                <telerik:RadTabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <Border x:Name="Step3Indicator" Style="{StaticResource StepIndicatorStyle}" Margin="0,0,12,0">
                                <TextBlock Text="3" FontWeight="Bold" FontSize="14" 
                                           Foreground="{StaticResource SecondaryTextBrush}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="Results" VerticalAlignment="Center" FontSize="14" FontWeight="SemiBold"/>
                        </StackPanel>
                    </DataTemplate>
                </telerik:RadTabItem.HeaderTemplate>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Results Header -->
                    <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="16,16,16,8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Schema Comparison Results" Style="{StaticResource HeaderTextStyle}"/>
                                <TextBlock x:Name="ResultsSummaryText" Style="{StaticResource InstructionTextStyle}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="ExportResultsButton" Content="📄 Export Results" 
                                        Style="{StaticResource SecondaryButtonStyle}" Margin="0,0,12,0"
                                        Click="ExportResultsButton_Click"/>
                                <Button x:Name="NewComparisonButton" Content="🔄 New Comparison" 
                                        Style="{StaticResource PrimaryButtonStyle}"
                                        Click="NewComparisonButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- Results Content -->
                    <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="16,8,16,16">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Filter and Search -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="200"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0" x:Name="SearchTextBox" 
                                         Tag="🔍 Search objects..." Margin="0,0,12,0"
                                         TextChanged="SearchTextBox_TextChanged"/>
                                
                                <TextBlock Grid.Column="1" Text="Filter by Status:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <ComboBox Grid.Column="2" x:Name="StatusFilterComboBox" 
                                          SelectionChanged="StatusFilterComboBox_SelectionChanged"/>
                            </Grid>
                            
                            <!-- Results Grid -->
                            <DataGrid Grid.Row="1" x:Name="ResultsGridView" 
                                      AutoGenerateColumns="False" 
                                      CanUserReorderColumns="True"
                                      CanUserResizeColumns="True"
                                      CanUserSortColumns="True"
                                      SelectionChanged="ResultsGridView_SelectionChanged">
                                <DataGrid.Columns>
                                    <DataGridTemplateColumn Header="Status" Width="120">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding Status, Converter={StaticResource StatusToIconConverter}}" 
                                                               Foreground="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                                               FontWeight="Bold" Margin="0,0,8,0"/>
                                                    <TextBlock Text="{Binding Status}" 
                                                               Foreground="{Binding Status, Converter={StaticResource StatusToColorConverter}}"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTextColumn Binding="{Binding ObjectType}" Header="Type" Width="100"/>
                                    <DataGridTextColumn Binding="{Binding Owner}" Header="Owner" Width="120"/>
                                    <DataGridTextColumn Binding="{Binding Name}" Header="Name" Width="200"/>
                                    <DataGridTextColumn Binding="{Binding LastModified}" Header="Last Modified" Width="150"/>
                                    <DataGridTextColumn Binding="{Binding Differences}" Header="Differences" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                            
                            <!-- Object Details -->
                            <Expander Grid.Row="2" x:Name="DetailsExpander" Header="Object Details" 
                                      IsExpanded="False" Margin="0,16,0,0">
                                <Grid Height="300">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                                        <StackPanel>
                                            <TextBlock Text="Source Definition" Style="{StaticResource SectionHeaderStyle}"/>
                                            <TextBox x:Name="SourceDefinitionTextBox"
                                                     Style="{StaticResource ReadOnlyTextBoxStyle}"
                                                     AcceptsReturn="True"
                                                     TextWrapping="Wrap"
                                                     VerticalScrollBarVisibility="Auto"
                                                     Height="200"/>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                                        <StackPanel>
                                            <TextBlock Text="Target Definition" Style="{StaticResource SectionHeaderStyle}"/>
                                            <TextBox x:Name="TargetDefinitionTextBox" 
                                                     IsReadOnly="True" 
                                                     AcceptsReturn="True"
                                                     TextWrapping="Wrap"
                                                     VerticalScrollBarVisibility="Auto"
                                                     Height="200"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </Expander>
                        </Grid>
                    </Border>
                </Grid>
            </telerik:RadTabItem>
        </telerik:RadTabControl>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="{StaticResource SecondaryBackgroundBrush}" 
                BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0" Padding="24,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusIcon" Text="🔄" FontSize="16" Margin="0,0,12,0"/>
                <TextBlock Grid.Column="1" x:Name="StatusBarTextBlock" Text="Ready to start - Configure database connections" 
                           VerticalAlignment="Center"/>
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Border x:Name="LoadingSpinner" Width="16" Height="16" Visibility="Collapsed"
                            BorderBrush="{StaticResource ProgressBrush}" BorderThickness="2" CornerRadius="8"
                            Margin="0,0,12,0">
                        <Border.RenderTransform>
                            <RotateTransform/>
                        </Border.RenderTransform>
                        <Border.Triggers>
                            <EventTrigger RoutedEvent="Border.Loaded">
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle"
                                                         From="0" To="360" Duration="0:0:1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </Border.Triggers>
                    </Border>
                    <TextBlock x:Name="StatusTimestamp" Text="" FontSize="12" 
                               Foreground="{StaticResource SecondaryTextBrush}" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>