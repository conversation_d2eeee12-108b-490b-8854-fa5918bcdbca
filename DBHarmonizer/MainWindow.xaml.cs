using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DBHarmonizer.Models;
using DBHarmonizer.Services;
using Serilog;
using Telerik.Windows.Controls;
using Microsoft.Win32;
using System.IO;

namespace DBHarmonizer
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly OracleSchemaService _oracleService;
        private readonly SchemaComparisonService _comparisonService;
        private DatabaseConnection _sourceConnection;
        private DatabaseConnection _targetConnection;
        private ComparisonResult? _currentResult;
        private ObservableCollection<SchemaObject> _filteredResults;

        public MainWindow()
        {
            InitializeComponent();
            
            _oracleService = new OracleSchemaService();
            _comparisonService = new SchemaComparisonService();
            _sourceConnection = new DatabaseConnection { Name = "Source" };
            _targetConnection = new DatabaseConnection { Name = "Target" };
            _filteredResults = new ObservableCollection<SchemaObject>();

            InitializeUI();
        }

        private void InitializeUI()
        {
            // Initialize status filter combo box
            StatusFilterComboBox.ItemsSource = Enum.GetValues(typeof(ComparisonStatus));
            StatusFilterComboBox.SelectedIndex = -1;

            // Set grid data source
            ResultsGridView.ItemsSource = _filteredResults;

            // Initialize tab states
            UpdateTabStates();
            UpdateStatusBar("Ready to start - Configure database connections", "🔄");

            // Initialize connection status indicators
            SourceStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");
            TargetStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");

            // Initialize button states
            TestSourceButton.IsEnabled = false;
            TestTargetButton.IsEnabled = false;
            NextToComparisonButton.IsEnabled = false;
            StartComparisonButton.IsEnabled = false;

            // Hide status panels initially
            SourceConnectionStatus.Visibility = Visibility.Collapsed;
            TargetConnectionStatus.Visibility = Visibility.Collapsed;

            Log.Information("MainWindow initialized successfully");
        }

        #region Tab Management

        private void MainTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (MainTabControl.SelectedItem is RadTabItem selectedTab)
            {
                UpdateStatusBarForTab(selectedTab);
            }
        }

        private void UpdateTabStates()
        {
            // Enable/disable tabs based on current state
            bool connectionsReady = _sourceConnection.IsConnected && _targetConnection.IsConnected;
            bool comparisonComplete = _currentResult != null && _currentResult.SchemaObjects.Count > 0;

            // Step 2: Comparison tab - enabled when both connections are ready
            ComparisonTab.IsEnabled = connectionsReady;
            
            // Step 3: Results tab - enabled when comparison is complete
            ResultsTab.IsEnabled = comparisonComplete;

            // Update navigation buttons
            NextToComparisonButton.IsEnabled = connectionsReady;
            StartComparisonButton.IsEnabled = connectionsReady;

            // Update connection summary
            if (connectionsReady)
            {
                ConnectionSummaryText.Text = $"✅ Source: {_sourceConnection.Server}:{_sourceConnection.Port}/{_sourceConnection.ServiceName}\n" +
                                           $"✅ Target: {_targetConnection.Server}:{_targetConnection.Port}/{_targetConnection.ServiceName}";
            }
            else
            {
                ConnectionSummaryText.Text = "❌ Please test both database connections successfully before proceeding.";
            }
        }

        private void UpdateStatusBarForTab(RadTabItem selectedTab)
        {
            if (selectedTab == ConnectionsTab)
            {
                UpdateStatusBar("Configure database connections", "🔧");
            }
            else if (selectedTab == ComparisonTab)
            {
                UpdateStatusBar("Configure schema comparison options", "⚙️");
            }
            else if (selectedTab == ResultsTab)
            {
                UpdateStatusBar("Review comparison results", "📊");
            }
        }

        private void UpdateStatusBar(string message, string icon = "🔄")
        {
            StatusBarTextBlock.Text = message;
            StatusIcon.Text = icon;
            StatusTimestamp.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        #endregion

        #region Connection Management

        private void SourceServerTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_sourceConnection != null)
            {
                _sourceConnection.Server = SourceServerTextBox.Text;
                UpdateTestButtonState(TestSourceButton, _sourceConnection);
            }
        }

        private void SourcePortTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_sourceConnection != null)
            {
                _sourceConnection.Port = SourcePortTextBox.Text;
                UpdateTestButtonState(TestSourceButton, _sourceConnection);
            }
        }

        private void SourceServiceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_sourceConnection != null)
            {
                _sourceConnection.ServiceName = SourceServiceTextBox.Text;
                UpdateTestButtonState(TestSourceButton, _sourceConnection);
            }
        }

        private void SourceUsernameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_sourceConnection != null)
            {
                _sourceConnection.Username = SourceUsernameTextBox.Text;
                UpdateTestButtonState(TestSourceButton, _sourceConnection);
            }
        }

        private void SourcePasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (_sourceConnection != null)
            {
                _sourceConnection.Password = SourcePasswordBox.Password;
                UpdateTestButtonState(TestSourceButton, _sourceConnection);
            }
        }

        private void TargetServerTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_targetConnection != null)
            {
                _targetConnection.Server = TargetServerTextBox.Text;
                UpdateTestButtonState(TestTargetButton, _targetConnection);
            }
        }

        private void TargetPortTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_targetConnection != null)
            {
                _targetConnection.Port = TargetPortTextBox.Text;
                UpdateTestButtonState(TestTargetButton, _targetConnection);
            }
        }

        private void TargetServiceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_targetConnection != null)
            {
                _targetConnection.ServiceName = TargetServiceTextBox.Text;
                UpdateTestButtonState(TestTargetButton, _targetConnection);
            }
        }

        private void TargetUsernameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_targetConnection != null)
            {
                _targetConnection.Username = TargetUsernameTextBox.Text;
                UpdateTestButtonState(TestTargetButton, _targetConnection);
            }
        }

        private void TargetPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (_targetConnection != null)
            {
                _targetConnection.Password = TargetPasswordBox.Password;
                UpdateTestButtonState(TestTargetButton, _targetConnection);
            }
        }

        private void UpdateTestButtonState(Button button, DatabaseConnection connection)
        {
            if (button != null && connection != null)
            {
                button.IsEnabled = connection.IsValid();
            }
        }

        private async void TestSourceButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection(_sourceConnection, "Source", TestSourceButton, SourceStatusIndicator, SourceConnectionStatus, SourceStatusText);
        }

        private async void TestTargetButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection(_targetConnection, "Target", TestTargetButton, TargetStatusIndicator, TargetConnectionStatus, TargetStatusText);
        }

        private async Task TestConnection(DatabaseConnection connection, string connectionName, Button button, 
            Border statusIndicator, Border statusPanel, TextBlock statusText)
        {
            try
            {
                // Show progress
                button.IsEnabled = false;
                button.Content = "🔄 Testing...";
                ShowProgress(true, $"Testing {connectionName} connection...");

                var isConnected = await _oracleService.TestConnectionAsync(connection);
                connection.IsConnected = isConnected;

                if (isConnected)
                {
                    button.Content = "✅ Connected";
                    button.Background = (SolidColorBrush)FindResource("SuccessBrush");
                    statusIndicator.Background = (SolidColorBrush)FindResource("SuccessBrush");
                    statusText.Text = $"✅ {connectionName} database connected successfully";
                    statusText.Foreground = (SolidColorBrush)FindResource("SuccessBrush");
                    statusPanel.Visibility = Visibility.Visible;
                    
                    Log.Information("{ConnectionName} database connection successful", connectionName);
                }
                else
                {
                    button.Content = "❌ Failed";
                    button.Background = (SolidColorBrush)FindResource("ErrorBrush");
                    statusIndicator.Background = (SolidColorBrush)FindResource("ErrorBrush");
                    statusText.Text = $"❌ Failed to connect to {connectionName} database. Please verify your connection settings.";
                    statusText.Foreground = (SolidColorBrush)FindResource("ErrorBrush");
                    statusPanel.Visibility = Visibility.Visible;
                    
                    MessageBox.Show($"Failed to connect to {connectionName} database.\n\nPlease check:\n• Server hostname/IP address\n• Port number (usually 1521)\n• Service name or SID\n• Username and password\n• Network connectivity", 
                                    "Connection Failed", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                button.Content = "❌ Error";
                button.Background = (SolidColorBrush)FindResource("ErrorBrush");
                statusIndicator.Background = (SolidColorBrush)FindResource("ErrorBrush");
                statusText.Text = $"⚠️ Connection error: {ex.Message}";
                statusText.Foreground = (SolidColorBrush)FindResource("ErrorBrush");
                statusPanel.Visibility = Visibility.Visible;
                
                Log.Error(ex, "Error testing {ConnectionName} connection", connectionName);
                
                MessageBox.Show($"Error testing {connectionName} connection:\n\n{ex.Message}\n\nPlease check your connection parameters and try again.",
                                "Connection Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowProgress(false);
                button.IsEnabled = true;
                UpdateTabStates();
                
                // Reset button appearance after delay if not connected
                if (!connection.IsConnected)
                {
                    await Task.Delay(3000);
                    button.Content = "🔍 Test Connection";
                    button.Background = (SolidColorBrush)FindResource("PrimaryNavyBrush");
                }
            }
        }

        #endregion

        #region Navigation

        private void NextToComparisonButton_Click(object sender, RoutedEventArgs e)
        {
            MainTabControl.SelectedItem = ComparisonTab;
        }

        private void BackToConnectionsButton_Click(object sender, RoutedEventArgs e)
        {
            MainTabControl.SelectedItem = ConnectionsTab;
        }

        #endregion

        #region Schema Comparison

        private void SelectAllObjectTypes_Click(object sender, RoutedEventArgs e)
        {
            SetAllObjectTypesChecked(true);
        }

        private void ClearAllObjectTypes_Click(object sender, RoutedEventArgs e)
        {
            SetAllObjectTypesChecked(false);
        }

        private void SetAllObjectTypesChecked(bool isChecked)
        {
            TablesCheckBox.IsChecked = isChecked;
            ViewsCheckBox.IsChecked = isChecked;
            IndexesCheckBox.IsChecked = isChecked;
            SequencesCheckBox.IsChecked = isChecked;
            TriggersCheckBox.IsChecked = isChecked;
            ProceduresCheckBox.IsChecked = isChecked;
            FunctionsCheckBox.IsChecked = isChecked;
            PackagesCheckBox.IsChecked = isChecked;
        }

        private List<SchemaObjectType> GetSelectedObjectTypes()
        {
            var selectedTypes = new List<SchemaObjectType>();
            
            if (TablesCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.Table);
            if (ViewsCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.View);
            if (IndexesCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.Index);
            if (SequencesCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.Sequence);
            if (TriggersCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.Trigger);
            if (ProceduresCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.Procedure);
            if (FunctionsCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.Function);
            if (PackagesCheckBox.IsChecked == true) selectedTypes.Add(SchemaObjectType.Package);
            
            return selectedTypes;
        }

        private async void StartComparisonButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedTypes = GetSelectedObjectTypes();
                if (!selectedTypes.Any())
                {
                    MessageBox.Show("Please select at least one object type to compare.",
                                    "No Object Types Selected", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Show progress
                ShowProgress(true, "Starting schema comparison...");
                StartComparisonButton.IsEnabled = false;

                var ownerFilter = string.IsNullOrWhiteSpace(OwnerFilterTextBox.Text) ? null : OwnerFilterTextBox.Text.Trim();

                var progress = new Progress<string>(message => UpdateStatusBar(message, "🔄"));

                _currentResult = await _comparisonService.CompareSchemas(
                    _sourceConnection, 
                    _targetConnection, 
                    selectedTypes, 
                    ownerFilter, 
                    progress);

                // Update results
                _filteredResults.Clear();
                foreach (var obj in _currentResult.SchemaObjects)
                {
                    _filteredResults.Add(obj);
                }

                // Update summary
                var summary = _currentResult.Summary;
                ResultsSummaryText.Text = $"Comparison completed in {_currentResult.Duration:mm\\:ss}. " +
                                        $"Found {summary.TotalObjects} objects: " +
                                        $"{summary.IdenticalObjects} identical, " +
                                        $"{summary.DifferentObjects} different, " +
                                        $"{summary.SourceOnlyObjects} source-only, " +
                                        $"{summary.TargetOnlyObjects} target-only";

                // Navigate to results tab
                UpdateTabStates();
                MainTabControl.SelectedItem = ResultsTab;
                
                UpdateStatusBar($"Comparison completed - {summary.TotalObjects} objects analyzed", "✅");
                
                Log.Information("Schema comparison completed successfully with {TotalObjects} objects", summary.TotalObjects);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error during schema comparison");
                UpdateStatusBar("Comparison failed", "❌");
                
                MessageBox.Show($"An error occurred during schema comparison:\n\n{ex.Message}",
                                "Comparison Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowProgress(false);
                StartComparisonButton.IsEnabled = true;
            }
        }

        #endregion

        #region Results Management

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            if (_currentResult == null) return;

            var searchText = SearchTextBox.Text?.ToLower() ?? string.Empty;
            var selectedStatus = StatusFilterComboBox.SelectedItem as ComparisonStatus?;

            var filtered = _currentResult.SchemaObjects.Where(obj =>
            {
                // Search filter
                bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                                   obj.Name.ToLower().Contains(searchText) ||
                                   obj.Owner.ToLower().Contains(searchText) ||
                                   obj.ObjectType.ToString().ToLower().Contains(searchText);

                // Status filter
                bool matchesStatus = selectedStatus == null || obj.Status == selectedStatus;

                return matchesSearch && matchesStatus;
            });

            _filteredResults.Clear();
            foreach (var obj in filtered)
            {
                _filteredResults.Add(obj);
            }
        }

        private async void ResultsGridView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ResultsGridView.SelectedItem is SchemaObject selectedObject)
            {
                try
                {
                    // Load definitions for comparison
                    var sourceDefinition = await _oracleService.GetObjectDefinitionAsync(_sourceConnection, selectedObject);
                    SourceDefinitionTextBox.Text = sourceDefinition;

                    // For objects that exist in both databases, get target definition
                    if (selectedObject.Status == ComparisonStatus.Identical || selectedObject.Status == ComparisonStatus.Different)
                    {
                        var targetDefinition = await _oracleService.GetObjectDefinitionAsync(_targetConnection, selectedObject);
                        TargetDefinitionTextBox.Text = targetDefinition;
                    }
                    else if (selectedObject.Status == ComparisonStatus.SourceOnly)
                    {
                        TargetDefinitionTextBox.Text = "Object does not exist in target database";
                    }
                    else if (selectedObject.Status == ComparisonStatus.TargetOnly)
                    {
                        SourceDefinitionTextBox.Text = "Object does not exist in source database";
                        var targetDefinition = await _oracleService.GetObjectDefinitionAsync(_targetConnection, selectedObject);
                        TargetDefinitionTextBox.Text = targetDefinition;
                    }

                    DetailsExpander.IsExpanded = true;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error loading object definitions for {ObjectName}", selectedObject.FullName);
                    SourceDefinitionTextBox.Text = $"Error loading definition: {ex.Message}";
                    TargetDefinitionTextBox.Text = $"Error loading definition: {ex.Message}";
                }
            }
        }

        private void ExportResultsButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentResult == null)
            {
                MessageBox.Show("No comparison results to export.",
                                "No Results", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var saveFileDialog = new SaveFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|Text files (*.txt)|*.txt|All files (*.*)|*.*",
                DefaultExt = "csv",
                FileName = $"SchemaComparison_{DateTime.Now:yyyyMMdd_HHmmss}"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ExportResults(saveFileDialog.FileName);
                    UpdateStatusBar($"Results exported to {System.IO.Path.GetFileName(saveFileDialog.FileName)}", "📄");
                    
                    MessageBox.Show($"Results successfully exported to:\n{saveFileDialog.FileName}",
                                    "Export Successful", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error exporting results");
                    MessageBox.Show($"Error exporting results:\n\n{ex.Message}",
                                    "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ExportResults(string fileName)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Status,Type,Owner,Name,LastModified,Differences");

            foreach (var obj in _currentResult!.SchemaObjects)
            {
                csv.AppendLine($"\"{obj.Status}\",\"{obj.ObjectType}\",\"{obj.Owner}\",\"{obj.Name}\",\"{obj.LastModified:yyyy-MM-dd HH:mm:ss}\",\"{obj.Differences?.Replace("\"", "\"\"")}\"");
            }

            File.WriteAllText(fileName, csv.ToString());
        }

        private void NewComparisonButton_Click(object sender, RoutedEventArgs e)
        {
            // Reset the application state
            _currentResult = null;
            _filteredResults.Clear();
            
            // Reset connections
            _sourceConnection.IsConnected = false;
            _targetConnection.IsConnected = false;
            
            // Reset UI
            ResetConnectionUI();
            UpdateTabStates();
            MainTabControl.SelectedItem = ConnectionsTab;
            
            UpdateStatusBar("Ready for new comparison - Configure database connections", "🔄");
        }

        private void ResetConnectionUI()
        {
            // Reset source connection UI
            TestSourceButton.Content = "🔍 Test Connection";
            TestSourceButton.Background = (SolidColorBrush)FindResource("PrimaryNavyBrush");
            SourceStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");
            SourceConnectionStatus.Visibility = Visibility.Collapsed;
            
            // Reset target connection UI
            TestTargetButton.Content = "🔍 Test Connection";
            TestTargetButton.Background = (SolidColorBrush)FindResource("PrimaryNavyBrush");
            TargetStatusIndicator.Background = (SolidColorBrush)FindResource("ProgressBackgroundBrush");
            TargetConnectionStatus.Visibility = Visibility.Collapsed;
            
            // Clear definition text boxes
            SourceDefinitionTextBox.Text = string.Empty;
            TargetDefinitionTextBox.Text = string.Empty;
            DetailsExpander.IsExpanded = false;
        }

        #endregion

        #region UI Helpers

        private void ShowProgress(bool show, string message = "")
        {
            LoadingSpinner.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            ConnectionProgressBar.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            ComparisonProgressBar.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            
            if (show && !string.IsNullOrEmpty(message))
            {
                UpdateStatusBar(message, "🔄");
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Settings functionality will be implemented in a future version.",
                            "Settings", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AboutButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("DBHarmonizer - Oracle Database Schema Structure Compare\n\n" +
                           "Version 1.0.0\n" +
                           "Built with Telerik UI for WPF and .NET 6\n\n" +
                           "This tool helps you compare Oracle database schemas\n" +
                           "and identify differences between database structures.",
                           "About DBHarmonizer", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion
    }
}